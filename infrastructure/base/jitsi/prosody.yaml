apiVersion: apps/v1
kind: Deployment
metadata:
  name: prosody
  namespace: jitsi-server
  labels:
    app.kubernetes.io/name: jitsi
    app.kubernetes.io/component: prosody
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: jitsi
      app.kubernetes.io/component: prosody
  template:
    metadata:
      labels:
        app.kubernetes.io/name: jitsi
        app.kubernetes.io/component: prosody
    spec:
      containers:
        - name: prosody
          image: jitsi/prosody:stable-8719
          ports:
            - containerPort: 5222
              name: xmpp-c2s
            - containerPort: 5347
              name: xmpp-component
            - containerPort: 5280
              name: bosh-http
          env:
            - name: XMPP_DOMAIN
              value: "meet.jitsi"
            - name: XMPP_AUTH_DOMAIN
              value: "auth.meet.jitsi"
            - name: XMPP_MUC_DOMAIN
              value: "muc.meet.jitsi"
            - name: XMPP_INTERNAL_MUC_DOMAIN
              value: "internal-muc.meet.jitsi"
            - name: XMPP_MODULES
              value: "muc_meeting_id,muc_domain_mapper,polls"
            - name: XMPP_MUC_MODULES
              value: "muc_meeting_id,muc_domain_mapper,polls"
            - name: XMPP_INTERNAL_MUC_MODULES
              value: "ping"
            - name: JICOFO_COMPONENT_SECRET
              valueFrom:
                secretKeyRef:
                  name: jitsi-secret
                  key: jicofo-component-secret
            - name: JVB_AUTH_USER
              value: "jvb"
            - name: JVB_AUTH_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: jitsi-secret
                  key: jvb-auth-password
            - name: JICOFO_AUTH_USER
              value: "focus"
            - name: JICOFO_AUTH_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: jitsi-secret
                  key: jicofo-auth-password
            - name: TZ
              value: "Asia/Ho_Chi_Minh"
            - name: PUBLIC_URL
              value: "https://jitsi.osp.vn"
            - name: ENABLE_GUESTS
              value: "1"
            - name: ENABLE_LOBBY
              value: "1"
            - name: ENABLE_AV_MODERATION
              value: "1"
            - name: ENABLE_BREAKOUT_ROOMS
              value: "1"
            - name: LOG_LEVEL
              value: "info"
          volumeMounts:
            - name: prosody-config
              mountPath: /config
            - name: prosody-plugins-custom
              mountPath: /prosody-plugins-custom
            - name: prosody-tls
              mountPath: /certs
              readOnly: true
          resources:
            requests:
              memory: "512Mi"
              cpu: "200m"
            limits:
              memory: "1Gi"
              cpu: "500m"
          livenessProbe:
            tcpSocket:
              port: 5222
            initialDelaySeconds: 30
            periodSeconds: 30
          readinessProbe:
            tcpSocket:
              port: 5222
            initialDelaySeconds: 10
            periodSeconds: 5
      volumes:
        - name: prosody-config
          configMap:
            name: prosody-config
        - name: prosody-plugins-custom
          emptyDir: {}
        - name: prosody-tls
          secret:
            secretName: prosody-tls
---
apiVersion: v1
kind: Service
metadata:
  name: prosody-service
  namespace: jitsi-server
  labels:
    app.kubernetes.io/name: jitsi
    app.kubernetes.io/component: prosody
spec:
  ports:
    - port: 5222
      targetPort: 5222
      name: xmpp-c2s
    - port: 5347
      targetPort: 5347
      name: xmpp-component
    - port: 5280
      targetPort: 5280
      name: bosh-http
  selector:
    app.kubernetes.io/name: jitsi
    app.kubernetes.io/component: prosody
  type: ClusterIP
---
# NodePort service for LAN access to Prosody WebSocket
apiVersion: v1
kind: Service
metadata:
  name: prosody-nodeport
  namespace: jitsi-server
  labels:
    app.kubernetes.io/name: jitsi
    app.kubernetes.io/component: prosody
spec:
  ports:
    - port: 5280
      targetPort: 5280
      nodePort: 30281
      name: bosh-http
  selector:
    app.kubernetes.io/name: jitsi
    app.kubernetes.io/component: prosody
  type: NodePort
