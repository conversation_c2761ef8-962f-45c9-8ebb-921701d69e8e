apiVersion: traefik.io/v1alpha1
kind: Middleware
metadata:
  name: jitsi-security-headers
  namespace: jitsi-server
spec:
  headers:
    customRequestHeaders:
      X-Forwarded-Proto: "https"
    customResponseHeaders:
      X-Frame-Options: "SAMEORIGIN"
      X-Content-Type-Options: "nosniff"
      Referrer-Policy: "strict-origin-when-cross-origin"
      Permissions-Policy: "camera=*, microphone=*, display-capture=*"
---
apiVersion: traefik.io/v1alpha1
kind: Middleware
metadata:
  name: jitsi-cors
  namespace: jitsi-server
spec:
  headers:
    accessControlAllowMethods:
      - GET
      - POST
      - PUT
      - DELETE
      - OPTIONS
    accessControlAllowOriginList:
      - "*"
    accessControlAllowHeaders:
      - "*"
    accessControlExposeHeaders:
      - "*"
    accessControlMaxAge: 86400
    addVaryHeader: true
---
apiVersion: traefik.io/v1alpha1
kind: Middleware
metadata:
  name: jitsi-websocket-headers
  namespace: jitsi-server
spec:
  headers:
    customRequestHeaders:
      X-Forwarded-Proto: "https"
      Connection: "Upgrade"
      Upgrade: "websocket"
---
apiVersion: traefik.io/v1alpha1
kind: IngressRoute
metadata:
  name: jitsi-web-ingress
  namespace: jitsi-server
  labels:
    app.kubernetes.io/name: jitsi
    app.kubernetes.io/component: ingress
spec:
  entryPoints:
    - websecure
  routes:
    - match: Host(`jitsi.osp.vn`)
      kind: Rule
      services:
        - name: jitsi-web-service
          port: 80
      middlewares:
        - name: jitsi-security-headers
        - name: jitsi-cors
    - match: Host(`jitsi.osp.vn`) && PathPrefix(`/xmpp-websocket`)
      kind: Rule
      services:
        - name: prosody-service
          port: 5280
      middlewares:
        - name: jitsi-websocket-headers
        - name: jitsi-cors
    - match: Host(`jitsi.osp.vn`) && PathPrefix(`/http-bind`)
      kind: Rule
      services:
        - name: prosody-service
          port: 5280
      middlewares:
        - name: jitsi-security-headers
        - name: jitsi-cors
    - match: Host(`jitsi.osp.vn`) && PathPrefix(`/colibri-ws/`)
      kind: Rule
      services:
        - name: jvb-service
          port: 8080
      middlewares:
        - name: jitsi-websocket-headers
        - name: jitsi-cors
---
# Simple HTTP IngressRoute for jitsi.osp.vn (no HTTPS redirect)
apiVersion: traefik.io/v1alpha1
kind: IngressRoute
metadata:
  name: jitsi-web-http
  namespace: jitsi-server
  labels:
    app.kubernetes.io/name: jitsi
    app.kubernetes.io/component: ingress
spec:
  entryPoints:
    - web
  routes:
    - match: Host(`jitsi.osp.vn`)
      kind: Rule
      services:
        - name: jitsi-web-service
          port: 80
      middlewares:
        - name: jitsi-security-headers
        - name: jitsi-cors
    - match: Host(`jitsi.osp.vn`) && PathPrefix(`/xmpp-websocket`)
      kind: Rule
      services:
        - name: prosody-service
          port: 5280
      middlewares:
        - name: jitsi-websocket-headers
        - name: jitsi-cors
    - match: Host(`jitsi.osp.vn`) && PathPrefix(`/http-bind`)
      kind: Rule
      services:
        - name: prosody-service
          port: 5280
      middlewares:
        - name: jitsi-security-headers
        - name: jitsi-cors
    - match: Host(`jitsi.osp.vn`) && PathPrefix(`/colibri-ws/`)
      kind: Rule
      services:
        - name: jvb-service
          port: 8080
      middlewares:
        - name: jitsi-websocket-headers
        - name: jitsi-cors
  tls:
    certResolver: letsencrypt

---
# IngressRoute for LAN access (HTTP only)
apiVersion: traefik.io/v1alpha1
kind: IngressRoute
metadata:
  name: jitsi-lan-ingress
  namespace: jitsi-server
  labels:
    app.kubernetes.io/name: jitsi
    app.kubernetes.io/component: ingress
spec:
  entryPoints:
    - web
  routes:
    - match: HostRegexp(`{host:192\.168\.1\.(96|81|87)}`) || HostRegexp(`{host:.*\.local}`)
      kind: Rule
      services:
        - name: jitsi-web-service
          port: 80
      middlewares:
        - name: jitsi-security-headers
        - name: jitsi-cors
    - match: (HostRegexp(`{host:192\.168\.1\.(96|81|87)}`) || HostRegexp(`{host:.*\.local}`)) && PathPrefix(`/xmpp-websocket`)
      kind: Rule
      services:
        - name: prosody-service
          port: 5280
      middlewares:
        - name: jitsi-websocket-headers
        - name: jitsi-cors
    - match: (HostRegexp(`{host:192\.168\.1\.(96|81|87)}`) || HostRegexp(`{host:.*\.local}`)) && PathPrefix(`/http-bind`)
      kind: Rule
      services:
        - name: prosody-service
          port: 5280
      middlewares:
        - name: jitsi-security-headers
        - name: jitsi-cors
    - match: (HostRegexp(`{host:192\.168\.1\.(96|81|87)}`) || HostRegexp(`{host:.*\.local}`)) && PathPrefix(`/colibri-ws/`)
      kind: Rule
      services:
        - name: jvb-service
          port: 8080
      middlewares:
        - name: jitsi-websocket-headers
        - name: jitsi-cors
